# QuickLoad Screenshot Templates and Guidelines

## Screenshot Strategy Overview

Create 6-8 screenshots that tell the story of your app's value proposition and key features. Each screenshot should have a clear purpose and appeal to your target audience.

## Screenshot Sequence Plan

### Screenshot 1: Welcome/User Selection
**Purpose**: Show the dual nature of the app (Dealer vs Driver)
**Screen**: Welcome screen with user type selection
**Caption**: "Choose Your Role - Dealer or Driver"
**Key Elements**:
- Clean welcome interface
- Two clear options: "I'm a Dealer" and "I'm a Driver"
- Professional truck/logistics imagery
- App logo prominently displayed

### Screenshot 2: Dealer Dashboard
**Purpose**: Showcase dealer functionality
**Screen**: Main dealer interface
**Caption**: "Post Your Freight Requirements Instantly"
**Key Elements**:
- Location selection (From/To)
- Vehicle type dropdown
- Goods type selection
- Weight input field
- "Find Trucks" button
- Clean, business-focused UI

### Screenshot 3: Driver Home Screen
**Purpose**: Show driver's perspective
**Screen**: Driver dashboard with available loads
**Caption**: "Find Profitable Loads Near You"
**Key Elements**:
- List of available loads
- Location-based results
- Load details (weight, distance, rate)
- "Find Load" prominent button
- GPS/location indicator

### Screenshot 4: Bidding Interface
**Purpose**: Highlight competitive bidding feature
**Screen**: Bid submission or active negotiations
**Caption**: "Negotiate Fair Rates with Real-Time Bidding"
**Key Elements**:
- Bid amount input
- Current bid status
- Negotiation chat interface
- Accept/Counter offer buttons
- Professional messaging UI

### Screenshot 5: Route Rate Monitor
**Purpose**: Show market intelligence feature
**Screen**: Rate monitoring dashboard
**Caption**: "Track Live Freight Rates Across Routes"
**Key Elements**:
- Route-wise rate listings
- Price trend indicators
- Popular routes highlighted
- Rate comparison data
- Professional data visualization

### Screenshot 6: Booking Management
**Purpose**: Demonstrate booking tracking
**Screen**: Active bookings or shipment tracking
**Caption**: "Manage Your Bookings Seamlessly"
**Key Elements**:
- Booking status timeline
- Pickup/delivery details
- Contact information
- Status updates
- Professional tracking interface

## Design Guidelines for Screenshots

### Device Frame:
- Use modern Android device frame (Pixel or Samsung Galaxy)
- Ensure frame doesn't overpower the content
- Consider frameless design for cleaner look

### Content Guidelines:
- **Realistic Data**: Use believable but anonymized information
- **Indian Context**: Use Indian city names, INR currency
- **Professional Look**: Business-appropriate color scheme
- **Readable Text**: Ensure all text is legible at thumbnail size
- **Consistent Branding**: Maintain color scheme across all screenshots

### Text Overlays (Optional):
- **Minimal Text**: Keep annotations brief and impactful
- **Key Benefits**: Highlight main value propositions
- **Call-to-Action**: Include subtle CTAs where appropriate

## Sample Data for Screenshots

### Location Data:
- **Routes**: Mumbai to Delhi, Bangalore to Chennai, Pune to Hyderabad
- **Cities**: Use major Indian commercial centers
- **Distances**: Realistic distances between cities

### Booking Data:
- **Goods Types**: Electronics, Textiles, Machinery, FMCG, Raw Materials
- **Vehicle Types**: 14ft Truck, 17ft Truck, 19ft Truck, 24ft Truck, Trailer
- **Weights**: 5 tons, 10 tons, 15 tons, 20 tons
- **Rates**: ₹15,000 - ₹45,000 (realistic freight rates)

### User Data:
- **Names**: Use common Indian business names
- **Companies**: Generic company names (ABC Logistics, XYZ Transport)
- **Phone Numbers**: Use placeholder format (+91 98XXX XXXXX)

## Technical Specifications

### Image Requirements:
- **Format**: PNG (preferred) or JPEG
- **Resolution**: 1080x1920 pixels (9:16 aspect ratio)
- **File Size**: Under 8MB per image
- **Quality**: High resolution for crisp display

### Device Compatibility:
- **Primary**: Focus on phone screenshots
- **Secondary**: Consider tablet versions if app supports tablets
- **Orientation**: Portrait orientation for phone screenshots

## Screenshot Creation Tools

### Professional Tools:
- **Figma**: Free design tool with device frames
- **Adobe XD**: Professional UI design tool
- **Sketch**: Mac-only design tool
- **Photoshop**: For advanced editing and effects

### Quick Tools:
- **Device Art Generator**: Google's free tool for device frames
- **Screenshot Framer**: Online tool for adding device frames
- **Canva**: Easy-to-use design tool with templates
- **MockuPhone**: Free mockup generator

### Mobile Tools:
- **Screenshot Easy**: For capturing app screenshots
- **Screen Master**: Android screenshot tool with editing
- **LongShot**: For capturing long scrolling screens

## Annotation and Callout Guidelines

### When to Use Annotations:
- Highlight unique features not immediately obvious
- Explain complex UI elements
- Draw attention to key value propositions
- Guide user's eye to important buttons/features

### Annotation Style:
- **Subtle**: Don't overpower the actual app interface
- **Consistent**: Use same style across all screenshots
- **Professional**: Match your app's design language
- **Readable**: Ensure text is legible at small sizes

### Common Annotation Types:
- **Arrows**: Point to specific features
- **Circles**: Highlight buttons or important elements
- **Text Boxes**: Explain features or benefits
- **Badges**: Show "New" or "Popular" features

## A/B Testing Considerations

### Test Different Approaches:
- **With vs Without Device Frames**: See which performs better
- **Annotated vs Clean**: Test if annotations help or distract
- **Different Sequences**: Try different screenshot orders
- **Various Captions**: Test different messaging approaches

### Metrics to Track:
- **Install Rate**: Conversion from store listing to install
- **Engagement**: Time spent viewing screenshots
- **User Feedback**: Reviews mentioning specific features
- **Competitor Comparison**: How your listing performs vs competitors

## Localization Considerations

### For Indian Market:
- **Language**: English is appropriate for business app
- **Currency**: Always show INR (₹) symbol
- **Locations**: Use recognizable Indian cities
- **Cultural Context**: Business-appropriate imagery and messaging

### Future Expansion:
- **Regional Languages**: Consider Hindi, Tamil, Telugu for broader reach
- **Local Imagery**: Use India-specific truck types and road scenes
- **Regional Routes**: Show popular freight routes in different regions
