# QuickLoad - Visual Assets Requirements for Google Play Store

## 1. App Icon Requirements

### Technical Specifications:
- **Format**: PNG (32-bit)
- **Size**: 512x512 pixels
- **File Size**: Under 1MB
- **Background**: Should work on various backgrounds
- **Safe Area**: Keep important elements within 426x426 pixels (center area)

### Design Guidelines:
- **Style**: Modern, professional, logistics-focused
- **Colors**: Use your brand colors (likely blue/orange based on logistics industry)
- **Elements**: 
  - Truck silhouette or icon
  - Letter "Q" or "QL" monogram
  - Speed/movement lines
  - Optional: Location pin or route elements
- **Avoid**: Text (except minimal branding), complex details, shadows

### Icon Concept Ideas:
1. **Truck + Route**: Stylized truck with curved route line
2. **Q + Truck**: Letter Q with truck integrated into the design
3. **Load Symbol**: Box/cargo with truck elements
4. **Network**: Connected dots representing logistics network

## 2. Feature Graphic Requirements

### Technical Specifications:
- **Format**: PNG or JPEG
- **Size**: 1024x500 pixels
- **File Size**: Under 15MB
- **Aspect Ratio**: 2.048:1

### Design Guidelines:
- **Purpose**: Main promotional image shown in Play Store
- **Content**: Should showcase key app features
- **Text**: Minimal, readable at small sizes
- **Branding**: Include app name and logo

### Feature Graphic Concept:
**Layout**: Split-screen design
- **Left Side (40%)**: App mockup showing main interface
- **Right Side (60%)**: 
  - "QuickLoad" app name
  - "Freight & Logistics Marketplace"
  - Key benefits: "Find Loads • Book Trucks • Track Shipments"
  - Truck/logistics graphics
- **Background**: Gradient or highway/logistics themed background
- **Colors**: Professional blue/orange color scheme

## 3. Screenshots Requirements

### Technical Specifications:
- **Format**: PNG or JPEG
- **Minimum**: 2 screenshots
- **Maximum**: 8 screenshots
- **Dimensions**: Match your target device resolutions
- **Recommended**: 1080x1920 pixels (9:16 aspect ratio)

### Screenshot Strategy:

#### Screenshot 1: Welcome/Onboarding Screen
- **Content**: User type selection (Dealer vs Driver)
- **Caption**: "Choose Your Role - Dealer or Driver"
- **Purpose**: Show dual-user nature of the app

#### Screenshot 2: Dealer Dashboard
- **Content**: Main dealer interface with booking options
- **Caption**: "Post Your Freight Requirements"
- **Elements**: Location selection, vehicle type, goods type

#### Screenshot 3: Driver Home Screen
- **Content**: Available loads, find load interface
- **Caption**: "Find Profitable Loads Near You"
- **Elements**: Load listings, location-based results

#### Screenshot 4: Bidding Interface
- **Content**: Bid submission or negotiation screen
- **Caption**: "Negotiate Fair Rates"
- **Elements**: Bidding interface, chat functionality

#### Screenshot 5: Route Rate Monitor
- **Content**: Rate monitoring dashboard
- **Caption**: "Real-Time Freight Rates"
- **Elements**: Route rates, price trends

#### Screenshot 6: Booking Summary
- **Content**: Booking confirmation or tracking
- **Caption**: "Track Your Shipments"
- **Elements**: Booking details, status updates

### Screenshot Design Guidelines:
- **Device Frames**: Use clean, modern device frames
- **Status Bar**: Show realistic status bar with good signal/battery
- **Content**: Use realistic but anonymized data
- **Annotations**: Add subtle callouts for key features
- **Consistency**: Maintain consistent UI theme across all screenshots

## 4. Additional Visual Assets (Optional but Recommended)

### Promotional Video (Optional)
- **Length**: 30 seconds maximum
- **Format**: MP4, MOV, or AVI
- **Size**: Up to 100MB
- **Content**: Quick app demo showing key features

### TV Banner (for Android TV - if applicable)
- **Size**: 1280x720 pixels
- **Format**: PNG or JPEG
- **Content**: App branding for TV interface

## 5. Asset Creation Tools & Resources

### Recommended Tools:
- **Professional**: Adobe Photoshop, Illustrator, Figma
- **Free Alternatives**: GIMP, Canva, Figma (free tier)
- **Icon Generators**: Android Asset Studio, App Icon Generator
- **Screenshot Tools**: Device Art Generator, Screenshot Framer

### Stock Resources:
- **Icons**: Feather Icons, Material Design Icons
- **Images**: Unsplash (logistics/truck photos)
- **Mockups**: Mockup World, Freepik

## 6. Brand Guidelines for Visual Assets

### Color Palette Suggestions:
- **Primary**: #2196F3 (Blue) - Trust, reliability
- **Secondary**: #FF9800 (Orange) - Energy, movement
- **Accent**: #4CAF50 (Green) - Success, go
- **Neutral**: #757575 (Gray) - Professional

### Typography:
- **Headers**: Roboto Bold or Montserrat Bold
- **Body**: Roboto Regular
- **UI Elements**: System fonts for consistency

### Visual Style:
- **Modern**: Clean, minimal design
- **Professional**: Business-appropriate aesthetics
- **Accessible**: High contrast, readable text
- **Consistent**: Unified design language across all assets
